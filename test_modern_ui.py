# -*- coding: utf-8 -*-
import sys
import os

from kivymd.app import <PERSON><PERSON><PERSON>
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.textfield import <PERSON><PERSON><PERSON>t<PERSON>ield
from kivymd.uix.button import MD<PERSON>aised<PERSON>utton, MDFlat<PERSON>utton
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivy.metrics import dp
from kivy.core.window import Window

class ModernUITest(MDApp):
    def build(self):
        Window.maximize()
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Indigo"
        self.theme_cls.accent_palette = "Cyan"
        
        # Main layout with modern gradient background
        main_layout = MDBoxLayout(
            orientation="vertical", 
            spacing=dp(0), 
            padding=dp(0),
            md_bg_color=[0.95, 0.97, 1, 1]  # Light blue-gray background
        )
        
        # Modern header with elevated card design
        header_card = MDCard(
            md_bg_color=[0.2, 0.3, 0.5, 1],  # Dark blue-gray
            elevation=8,
            radius=[0, 0, 20, 20],
            size_hint_y=None,
            height=dp(100),
            padding=dp(20)
        )
        
        header = MDBoxLayout(orientation="horizontal", spacing=dp(15))
        
        # Modern title with better typography
        title_layout = MDBoxLayout(orientation="vertical", size_hint_x=0.25)
        title = MDLabel(
            text="Ticketing Task Manager", 
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],  # White text
            font_style="H4", 
            bold=True,
            halign="left"
        )
        subtitle = MDLabel(
            text="Streamlined Task Processing", 
            theme_text_color="Custom",
            text_color=[0.8, 0.9, 1, 1],  # Light blue text
            font_style="Subtitle1",
            halign="left"
        )
        title_layout.add_widget(title)
        title_layout.add_widget(subtitle)
        
        # Modern stats display with cards
        stats_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), size_hint_x=0.35)
        
        # Time display card
        time_card = MDCard(
            md_bg_color=[0.1, 0.7, 0.9, 0.9],  # Cyan with transparency
            radius=15,
            elevation=4,
            padding=dp(10),
            size_hint_x=0.5
        )
        time_layout = MDBoxLayout(orientation="vertical")
        time_display = MDLabel(
            text="0.00 min", 
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            font_style="H6", 
            bold=True,
            halign="center"
        )
        time_label = MDLabel(
            text="Total Time", 
            theme_text_color="Custom",
            text_color=[0.9, 0.95, 1, 1],
            font_style="Caption",
            halign="center"
        )
        time_layout.add_widget(time_display)
        time_layout.add_widget(time_label)
        time_card.add_widget(time_layout)
        
        # Titles display card
        titles_card = MDCard(
            md_bg_color=[0.4, 0.8, 0.4, 0.9],  # Green with transparency
            radius=15,
            elevation=4,
            padding=dp(10),
            size_hint_x=0.5
        )
        titles_layout = MDBoxLayout(orientation="vertical")
        titles_display = MDLabel(
            text="0", 
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            font_style="H6", 
            bold=True,
            halign="center"
        )
        titles_label = MDLabel(
            text="Titles Processed", 
            theme_text_color="Custom",
            text_color=[0.9, 0.95, 1, 1],
            font_style="Caption",
            halign="center"
        )
        titles_layout.add_widget(titles_display)
        titles_layout.add_widget(titles_label)
        titles_card.add_widget(titles_layout)
        
        stats_layout.add_widget(time_card)
        stats_layout.add_widget(titles_card)
        
        # Modern action buttons with better styling
        buttons_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), size_hint_x=0.4)
        
        get_task_btn = MDRaisedButton(
            text="Get Task", 
            md_bg_color=[0.5, 0.2, 0.8, 1],  # Purple
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            radius=20,
            size_hint_x=0.25,
            on_release=self.show_dialog
        )

        break_btn = MDRaisedButton(
            text="Pause", 
            md_bg_color=[1, 0.6, 0.2, 1],  # Orange
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            radius=20,
            size_hint_x=0.25,
            on_release=self.show_dialog
        )

        active_btn = MDRaisedButton(
            text="Play", 
            md_bg_color=[0.2, 0.8, 0.4, 1],  # Green
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            radius=20,
            size_hint_x=0.25,
            on_release=self.show_dialog
        )

        submit_btn = MDRaisedButton(
            text="Submit", 
            md_bg_color=[0.8, 0.2, 0.3, 1],  # Red
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            radius=20,
            size_hint_x=0.25,
            on_release=self.show_dialog
        )

        buttons_layout.add_widget(get_task_btn)
        buttons_layout.add_widget(break_btn)
        buttons_layout.add_widget(active_btn)
        buttons_layout.add_widget(submit_btn)

        header.add_widget(title_layout)
        header.add_widget(stats_layout)
        header.add_widget(buttons_layout)
        header_card.add_widget(header)
        main_layout.add_widget(header_card)
        
        # Sample form content
        content_layout = MDBoxLayout(orientation="vertical", spacing=dp(20), padding=dp(25))
        
        # Modern form card
        form_card = MDCard(
            md_bg_color=[1, 1, 1, 1],  # Pure white
            elevation=12,
            radius=25,
            padding=dp(30), 
            spacing=dp(20), 
            adaptive_height=True
        )
        
        form_layout = MDBoxLayout(orientation="vertical", spacing=dp(20), adaptive_height=True)
        
        # Sample field
        field_container = MDCard(
            md_bg_color=[0.98, 0.99, 1, 1],  # Very light blue
            elevation=2,
            radius=15,
            padding=dp(15),
            spacing=dp(10),
            adaptive_height=True,
            size_hint_y=None,
            height=dp(120)
        )
        
        field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(15))
        
        label = MDLabel(
            text="Sample Field:", 
            size_hint_x=0.3, 
            theme_text_color="Custom",
            text_color=[0.2, 0.3, 0.5, 1],  # Dark blue-gray
            font_style="Subtitle1",
            bold=True,
            halign="left"
        )
        
        text_field = MDTextField(
            text="", 
            multiline=True, 
            size_hint_x=0.7,
            mode="outlined",
            line_color_normal=[0.7, 0.8, 0.9, 1],
            line_color_focus=[0.2, 0.6, 1, 1],
            text_color_normal=[0.2, 0.3, 0.5, 1],
            text_color_focus=[0.1, 0.2, 0.4, 1],
            fill_color_normal=[1, 1, 1, 1],
            fill_color_focus=[0.98, 0.99, 1, 1]
        )
        
        field_layout.add_widget(label)
        field_layout.add_widget(text_field)
        field_container.add_widget(field_layout)
        form_layout.add_widget(field_container)
        
        form_card.add_widget(form_layout)
        content_layout.add_widget(form_card)
        main_layout.add_widget(content_layout)
        
        return main_layout
    
    def show_dialog(self, instance):
        dialog = MDDialog(
            title="✅ Modern UI Test",
            text="The modernized UI is working perfectly!",
            buttons=[
                MDRaisedButton(
                    text="OK", 
                    md_bg_color=[0.2, 0.8, 0.4, 1],
                    theme_text_color="Custom",
                    text_color=[1, 1, 1, 1],
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

if __name__ == "__main__":
    ModernUITest().run()
