# -*- coding: utf-8 -*-
import sys
import os
import json
import pickle
import atexit

# Ensure UTF-8 encoding for all text operations
if sys.version_info[0] >= 3:
    try:
        import io
        if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.buffer:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        pass  # Skip if already configured or not available

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.core.text import LabelBase
import pyodbc
import datetime
import getpass

import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
import time
import unicodedata
import pyperclip

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\Ticketing Process.accdb"

# Field headers
fields = [
    "Fresh/Backlog", "Ticket Owner", "Ticket ID", "Title Description", "Queue", "Created Date",
    "Last Assigned", "Age", "DF Age", "Action Date",
    "Business Line", "Partner", "Market Place", "Identifiers(GTI/VCID/ASIN)", "Title", "Content Type", "Action Taken", "Group Assigned", "Single/Double", "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2", "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3", "Titles Received", "Titles Worked",
    "Comments"
    ]

# Dropdown options
BUSINESS_LINE_OPTIONS = ["EST", "VOD", "3p Subs", "SVOD", "N/A"]

CONTENT_TYPE_OPTIONS = ["Movie", "Series", "N/A"]

GROUP_ASSIGNED_OPTIONS = [
    "Encoding", "Redelivery Group", "DFSubtitleFix", "Support engineering", "Trust&Safety",
    "Merge", "Amazon Studios", "CAM", "POM", "Offers", "PVD", "Ownership",
    "Asset Management", "Internal DF team", "N/A"
]

TICKET_STATUS_OPTIONS = [
    "Resolved", "Reassigned", "Pending Verification of Fix", "Pending of DF -Wip",
    "Reached out (Requestor Team) for Addicional Info/Clarity", "Follow Up"
]

ISSUE_TYPE_OPTIONS = [
    "Mapping", "Ratings", "VCID", "Merge Issue", "Artwork", "Incorrect Content",
    "Playback", "Encoding", "Content Descriptor", "Availability", "Subtitle",
    "Audio", "Video", "Metadata", "Offer", "Assets", "Avails", "Duplicate", "N/A"
]

ISSUE_SUBTYPE_OPTIONS = [
    "Artwork - Incorrect Artwork", "Artwork - Missing", "Assets", "Assets - Inconsistent within Epsiodes",
    "Assets - Missing", "Audio - Duration difference", "Audio - Incorrect Audio", "Audio - Missing",
    "Audio - Out of sync", "Audio - Quality", "Availability - Content unavailable due to tech error",
    "Avails - Holdback issues", "Avails - Incorrect avails delivered", "Avails - Not delivered",
    "Avails - Pricing Issue", "Encoding Issue", "Merge Issue", "Metadata - Missing",
    "Metadata - Quality issue (incorrect details)", "Metadata - Ratings/ Content descriptors",
    "Metadata - Sequencing issue", "Metadata - Synopsis issue", "Offer - Take down / Revoke request",
    "Offers - Expired offers", "Offers - Future offers", "Offers - In conflict",
    "Offers - Inconsistence", "Offers - Not created", "Offers - Pricing missmatch",
    "Playback - Video error in site", "POM matrix updating request", "Request - Mapping",
    "Subtitle - Incorrect Subtitle", "Subtitle - Missing", "Subtitle - Out of sync",
    "Subtitle - Quality", "Video - Incorrect Content", "Video - Missing", "Video - Out of VC spec",
    "Video - Quality", "Availability - Content unavailable due to internal error",
    "Trailer - Playback", "Trailer - Missing", "N/A"
]

class TVSeriesForm(MDApp):
    def __init__(self):
        super().__init__()
        self.entries = {}
        self.selected_issues = []
        self.selected_rca_items = []
        self.offer_type_menu = None
        self.user_dropdown = None

        # Dropdown menus for various fields
        self.business_line_menu = None
        self.content_type_menu = None
        self.group_assigned_menu = None
        self.ticket_status_menu = None
        self.issue_type_menus = {}  # For Issue Type 1, 2, 3
        self.issue_subtype_menus = {}  # For Issue Subtype 1, 2, 3
        self.edp_link_url = ""
        self.current_gti_index = 0
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        self.lob_menu = None
        self.code_expansion_menu = None
        self.action_menu = None
        self.resolver_menu = None
        self.start_time = None
        self.end_time = None
        self.break_start_time = None  # datetime.time object for tracking breaks internally
        self.break_end_time = None    # datetime.time object for tracking breaks internally
        self.total_break_time = 0.0   # Total break time in minutes (float)
        self.break_duration_db = 0.0   # Break duration saved to database
        self.issue_name = ""  # Store the button name separately
        self.form_counter = 1  # Track number of forms
        self.additional_forms = []  # Store additional form data
        self.active_form = 'main'  # Track which form is currently active
        self.cumulative_time = 0.0  # Store the cumulative total time for display
        self.titles_processed = 0  # Counter for completed/submitted titles
        self.autosave_path = os.path.join(os.path.expanduser("~"), ".tv_series_tool_autosave.pkl")
        
        # Season/Episode data for View Details functionality
        self.current_season_episode_data = ""
        self.current_territory_data = ""

        # Get Task functionality
        self.task_allocation_data = []
        self.current_task_data = None
        self.ticket_owner_dropdown = None
        self.processed_ticket_ids = set()  # Track unique processed Ticket IDs
        
        # Font setup will be done in build() method after Kivy initialization

        # Initialize cursor settings
        self.setup_cursor_management()

        # Register auto-save on exit
        atexit.register(self.auto_save_data)

        # Try to load previous session data
        self.try_load_autosave()
    
    def setup_unicode_support(self):
        """Setup Unicode font support for international characters"""
        try:
            # Register fonts that support Japanese characters
            unicode_fonts = [
                'C:/Windows/Fonts/msgothic.ttc',  # MS Gothic - supports Japanese
                'C:/Windows/Fonts/msmincho.ttc',  # MS Mincho - supports Japanese
                'C:/Windows/Fonts/meiryo.ttc',    # Meiryo - supports Japanese
                'C:/Windows/Fonts/arial.ttf',     # Arial Unicode
                'C:/Windows/Fonts/calibri.ttf',   # Calibri
                'C:/Windows/Fonts/segoeui.ttf'    # Segoe UI
            ]

            print("Checking for Unicode fonts...")
            font_registered = False
            for font_path in unicode_fonts:
                print(f"Checking font: {font_path}")
                if os.path.exists(font_path):
                    try:
                        LabelBase.register(name='UnicodeFont', fn_regular=font_path)
                        print(f"Successfully registered Unicode font: {font_path}")
                        font_registered = True
                        break
                    except Exception as font_error:
                        print(f"Failed to register font {font_path}: {font_error}")
                        continue
                else:
                    print(f"Font not found: {font_path}")

            if not font_registered:
                print("Warning: No Unicode-compatible font found, using default font")
                # Try to register a fallback font
                try:
                    LabelBase.register(name='UnicodeFont', fn_regular=None)  # Use default
                    print("Registered default font as UnicodeFont")
                except Exception as fallback_error:
                    print(f"Failed to register fallback font: {fallback_error}")

        except Exception as e:
            print(f"Font registration error: {e}")

    def setup_cursor_management(self):
        """Setup cursor management to prevent cursor disappearing"""
        try:
            # Force cursor to be visible
            from kivy.core.window import Window
            Window.show_cursor = True

            # Set default cursor
            try:
                Window.set_system_cursor('arrow')
            except:
                # Fallback if system cursor setting fails
                pass

            print("Cursor management initialized")
        except Exception as e:
            print(f"Cursor management warning: {e}")

    def normalize_unicode_text(self, text):
        """Normalize Unicode text to handle international characters properly"""
        if not text:
            return ""
        try:
            # Convert to string first
            text_str = str(text)
            # Normalize Unicode text to NFC form for consistent character representation
            normalized = unicodedata.normalize('NFC', text_str)
            # Ensure proper encoding for web forms
            encoded = normalized.encode('utf-8').decode('utf-8')
            return encoded
        except Exception as e:
            print(f"Unicode normalization error: {e}")
            # Fallback to basic string conversion
            try:
                return str(text).encode('utf-8', errors='replace').decode('utf-8')
            except:
                return str(text)

    def keyboard_handler(self, window, key, *args):
        # Prevent Escape key from closing the application
        if key == 27:  # 27 is the keycode for Escape key
            return True  # Return True to indicate the key was handled
        return False  # Let other keys pass through

    def on_cursor_enter(self, window, *args):
        """Handle cursor entering the window"""
        Window.show_cursor = True
        Window.set_system_cursor('arrow')
        return True

    def on_cursor_leave(self, window, *args):
        """Handle cursor leaving the window"""
        Window.show_cursor = True
        return True

    def on_mouse_motion(self, window, pos, *args):
        """Handle mouse motion to ensure cursor remains visible"""
        Window.show_cursor = True
        return False  # Allow other handlers to process the motion event

    def check_cursor_visibility(self, dt):
        """Periodic check to ensure cursor remains visible"""
        try:
            Window.show_cursor = True
            # Optionally reset cursor type if needed
            if hasattr(Window, 'set_system_cursor'):
                try:
                    Window.set_system_cursor('arrow')
                except:
                    pass
        except Exception as e:
            print(f"Cursor visibility check error: {e}")
        return True  # Continue scheduling
        
    def build(self):
        # Configure font for Unicode support first
        self.setup_unicode_support()

        Window.maximize()
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Indigo"
        self.theme_cls.accent_palette = "Cyan"

        # Ensure cursor remains visible
        Window.show_cursor = True
        Window.set_system_cursor('arrow')

        # Bind keyboard to prevent Escape key from closing the app
        Window.bind(on_keyboard=self.keyboard_handler)

        # Bind mouse events to ensure cursor visibility
        Window.bind(on_cursor_enter=self.on_cursor_enter)
        Window.bind(on_cursor_leave=self.on_cursor_leave)
        Window.bind(on_motion=self.on_mouse_motion)

        # Schedule periodic cursor check
        from kivy.clock import Clock
        Clock.schedule_interval(self.check_cursor_visibility, 1.0)  # Check every second

        # Main layout with modern gradient background
        main_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(0),
            padding=dp(0),
            md_bg_color=[0.95, 0.97, 1, 1]  # Light blue-gray background
        )

        # Modern header with elevated card design
        header_card = MDCard(
            md_bg_color=[0.2, 0.3, 0.5, 1],  # Dark blue-gray
            elevation=8,
            radius=[0, 0, 20, 20],
            size_hint_y=None,
            height=dp(100),
            padding=dp(20)
        )

        header = MDBoxLayout(orientation="horizontal", spacing=dp(15))

        # Modern title with better typography
        title_layout = MDBoxLayout(orientation="vertical", size_hint_x=0.25)
        title = MDLabel(
            text="Ticketing Task Manager",
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],  # White text
            font_style="H4",
            bold=True,
            halign="left"
        )
        subtitle = MDLabel(
            text="Streamlined Task Processing",
            theme_text_color="Custom",
            text_color=[0.8, 0.9, 1, 1],  # Light blue text
            font_style="Subtitle1",
            halign="left"
        )
        title_layout.add_widget(title)
        title_layout.add_widget(subtitle)

        # Modern stats display with cards
        stats_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), size_hint_x=0.35)

        # Time display card
        time_card = MDCard(
            md_bg_color=[0.1, 0.7, 0.9, 0.9],  # Cyan with transparency
            radius=15,
            elevation=4,
            padding=dp(10),
            size_hint_x=0.5
        )
        time_layout = MDBoxLayout(orientation="vertical")
        self.time_display = MDLabel(
            text="0.00 min",
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            font_style="H6",
            bold=True,
            halign="center"
        )
        time_label = MDLabel(
            text="Total Time",
            theme_text_color="Custom",
            text_color=[0.9, 0.95, 1, 1],
            font_style="Caption",
            halign="center"
        )
        time_layout.add_widget(self.time_display)
        time_layout.add_widget(time_label)
        time_card.add_widget(time_layout)

        # Titles display card
        titles_card = MDCard(
            md_bg_color=[0.4, 0.8, 0.4, 0.9],  # Green with transparency
            radius=15,
            elevation=4,
            padding=dp(10),
            size_hint_x=0.5
        )
        titles_layout = MDBoxLayout(orientation="vertical")
        self.titles_display = MDLabel(
            text="0",
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            font_style="H6",
            bold=True,
            halign="center"
        )
        titles_label = MDLabel(
            text="Titles Processed",
            theme_text_color="Custom",
            text_color=[0.9, 0.95, 1, 1],
            font_style="Caption",
            halign="center"
        )
        titles_layout.add_widget(self.titles_display)
        titles_layout.add_widget(titles_label)
        titles_card.add_widget(titles_layout)

        stats_layout.add_widget(time_card)
        stats_layout.add_widget(titles_card)

        # Modern action buttons with better styling
        buttons_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), size_hint_x=0.4)

        get_task_btn = MDRaisedButton(
            text="Get Task",
            md_bg_color=[0.5, 0.2, 0.8, 1],  # Purple
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            size_hint_x=0.25,
            on_release=self.get_task
        )

        break_btn = MDRaisedButton(
            text="Pause",
            md_bg_color=[1, 0.6, 0.2, 1],  # Orange
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            size_hint_x=0.25,
            on_release=self.set_break_mode
        )

        active_btn = MDRaisedButton(
            text="Play",
            md_bg_color=[0.2, 0.8, 0.4, 1],  # Green
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            size_hint_x=0.25,
            on_release=self.set_active_mode
        )

        submit_btn = MDRaisedButton(
            text="Submit",
            md_bg_color=[0.8, 0.2, 0.3, 1],  # Red
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=6,
            size_hint_x=0.25,
            on_release=self.submit_task
        )

        buttons_layout.add_widget(get_task_btn)
        buttons_layout.add_widget(break_btn)
        buttons_layout.add_widget(active_btn)
        buttons_layout.add_widget(submit_btn)

        header.add_widget(title_layout)
        header.add_widget(stats_layout)
        header.add_widget(buttons_layout)
        header_card.add_widget(header)
        main_layout.add_widget(header_card)

        # Modern content layout (simplified without scroll for compatibility)
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            padding=dp(25)
        )

        # Modern form card with elevated design
        form_card = MDCard(
            md_bg_color=[1, 1, 1, 1],  # Pure white
            elevation=12,
            radius=25,
            padding=dp(30),
            spacing=dp(20),
            adaptive_height=True
        )
        form_layout = MDBoxLayout(orientation="vertical", spacing=dp(20), adaptive_height=True)

        for field in fields:
            # Modern field container with subtle background
            field_container = MDCard(
                md_bg_color=[0.98, 0.99, 1, 1],  # Very light blue
                elevation=2,
                radius=15,
                padding=dp(15),
                spacing=dp(10),
                adaptive_height=True,
                size_hint_y=None
            )

            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(15),
                                     adaptive_height=True, size_hint_y=None)

            # Modern label with better typography
            label = MDLabel(
                text=field,
                size_hint_x=0.3,
                theme_text_color="Custom",
                text_color=[0.2, 0.3, 0.5, 1],  # Dark blue-gray
                font_style="Subtitle1",
                bold=True,
                halign="left"
            )

            if field in ["Offer Type", "Business Line", "Content Type", "Group Assigned", "Ticket Status",
                         "Issue Type 1", "Issue Subtype 1", "Issue Type 2", "Issue Subtype 2",
                         "Issue Type 3", "Issue Subtype 3"]:
                # Modern dropdown fields
                field_layout.add_widget(label)

                # Create modern dropdown button with appropriate text
                if field == "Offer Type":
                    button_text = "Select Offer Type"
                elif field == "Business Line":
                    button_text = "Select Business Line"
                elif field == "Content Type":
                    button_text = "Select Content Type"
                elif field == "Group Assigned":
                    button_text = "Select Group Assigned"
                elif field == "Ticket Status":
                    button_text = "Select Ticket Status"
                elif "Issue Type" in field:
                    button_text = f"Select {field}"
                elif "Issue Subtype" in field:
                    button_text = f"Select {field}"
                else:
                    button_text = f"Select {field}"

                dropdown_field = MDRaisedButton(
                    text=button_text,
                    size_hint_x=0.7,
                    md_bg_color=[0.9, 0.95, 1, 1],  # Light blue
                    theme_text_color="Custom",
                    text_color=[0.2, 0.3, 0.5, 1],  # Dark blue-gray text
                    elevation=4,
                    on_release=lambda x, f=field: self.open_dropdown_menu(x, f)
                )
                self.entries[field] = dropdown_field
                field_layout.add_widget(dropdown_field)
            else:
                # Modern text fields with better styling
                field_layout.add_widget(label)
                text_field = MDTextField(
                    text="",
                    multiline=True,
                    size_hint_x=0.7,
                    max_text_length=4000,
                    font_name='UnicodeFont',
                    mode="outlined",
                    line_color_normal=[0.7, 0.8, 0.9, 1],
                    line_color_focus=[0.2, 0.6, 1, 1],
                    text_color_normal=[0.2, 0.3, 0.5, 1],
                    text_color_focus=[0.1, 0.2, 0.4, 1],
                    fill_color_normal=[1, 1, 1, 1],
                    fill_color_focus=[0.98, 0.99, 1, 1]
                )
                text_field.height = dp(120)
                text_field.bind(on_key_down=self.on_key_down)
                text_field.bind(text=self.on_text_change)
                text_field.bind(focus=self.on_text_field_focus)
                if field == "Season/Episode Number":
                    text_field.bind(on_text_validate=self.format_season_episode)
                    text_field.bind(focus=self.on_season_episode_focus)
                self.entries[field] = text_field
                field_layout.add_widget(text_field)

            field_layout.height = dp(120)
            field_container.add_widget(field_layout)
            form_layout.add_widget(field_container)

        # Modern Add/Remove buttons with better styling
        add_btn_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(80),
            spacing=dp(15),
            padding=[dp(20), dp(10), dp(20), dp(10)]
        )
        add_btn_layout.add_widget(MDLabel(text="", size_hint_x=0.5))  # Spacer

        add_btn = MDRaisedButton(
            text="Add Form +",
            size_hint_x=0.25,
            md_bg_color=[0.2, 0.8, 0.4, 1],  # Green
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=8,
            on_release=self.add_new_form
        )
        remove_btn = MDRaisedButton(
            text="Remove Form -",
            size_hint_x=0.25,
            md_bg_color=[0.9, 0.3, 0.3, 1],  # Red
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=8,
            on_release=self.remove_form
        )
        add_btn_layout.add_widget(add_btn)
        add_btn_layout.add_widget(remove_btn)
        form_layout.add_widget(add_btn_layout)

        # Add form to card
        form_card.add_widget(form_layout)
        content.add_widget(form_card)

        main_layout.add_widget(content)

        return main_layout

    def on_text_change(self, instance, text):
        """Handle text changes with Unicode normalization"""
        try:
            normalized_text = self.normalize_unicode_text(text)
            if normalized_text != text:
                instance.text = normalized_text

            # Auto-save after significant text changes
            # Use a simple counter to avoid saving too frequently
            if not hasattr(self, '_text_change_counter'):
                self._text_change_counter = 0

            self._text_change_counter += 1
            if self._text_change_counter >= 20:  # Save every 20 text changes
                self.auto_save_data()
                self._text_change_counter = 0
        except Exception as e:
            print(f"Text normalization error: {e}")

    def open_offer_menu_click(self, instance):
        if not hasattr(self, 'selected_offers'):
            self.selected_offers = set()

        menu_items = [
            {"text": "Prime", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Prime")},
            {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("TVOD")},
            {"text": "Channels", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Channels")},
            {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("FVOD")},
            {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("AVOD")},
            {"text": "POEST", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("POEST")},
            {"text": "SVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("SVOD")},
        ]
        self.offer_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.offer_type_menu.open()

    def toggle_offer_type(self, value):
        if value in self.selected_offers:
            self.selected_offers.remove(value)
        else:
            self.selected_offers.add(value)

        # Update button text with selected offers
        if self.selected_offers:
            self.entries["Offer Type"].text = ", ".join(sorted(self.selected_offers))
        else:
            self.entries["Offer Type"].text = "Select Offer Type"

    def open_dropdown_menu(self, instance, field):
        """Open dropdown menu for various fields"""
        if field == "Offer Type":
            self.open_offer_menu_click(instance)
        elif field == "Business Line":
            self.open_business_line_menu(instance)
        elif field == "Content Type":
            self.open_content_type_menu(instance)
        elif field == "Group Assigned":
            self.open_group_assigned_menu(instance)
        elif field == "Ticket Status":
            self.open_ticket_status_menu(instance)
        elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
            self.open_issue_type_menu(instance, field)
        elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
            self.open_issue_subtype_menu(instance, field)

    def open_business_line_menu(self, instance):
        """Open Business Line dropdown menu"""
        menu_items = []
        for option in BUSINESS_LINE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Business Line")
            })
        self.business_line_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.business_line_menu.open()

    def open_content_type_menu(self, instance):
        """Open Content Type dropdown menu"""
        menu_items = []
        for option in CONTENT_TYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Content Type")
            })
        self.content_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.content_type_menu.open()

    def open_group_assigned_menu(self, instance):
        """Open Group Assigned dropdown menu"""
        menu_items = []
        for option in GROUP_ASSIGNED_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Group Assigned")
            })
        self.group_assigned_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.group_assigned_menu.open()

    def open_ticket_status_menu(self, instance):
        """Open Ticket Status dropdown menu"""
        menu_items = []
        for option in TICKET_STATUS_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Ticket Status")
            })
        self.ticket_status_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.ticket_status_menu.open()

    def open_issue_type_menu(self, instance, field):
        """Open Issue Type dropdown menu"""
        menu_items = []
        for option in ISSUE_TYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, field)
            })
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.issue_type_menus[field] = menu
        menu.open()

    def open_issue_subtype_menu(self, instance, field):
        """Open Issue Subtype dropdown menu"""
        menu_items = []
        for option in ISSUE_SUBTYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, field)
            })
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.issue_subtype_menus[field] = menu
        menu.open()

    def set_dropdown_value(self, button, value, field):
        """Set dropdown value and dismiss menu"""
        button.text = value

        # Dismiss the appropriate menu
        if field == "Business Line" and self.business_line_menu:
            self.business_line_menu.dismiss()
        elif field == "Content Type" and self.content_type_menu:
            self.content_type_menu.dismiss()
        elif field == "Group Assigned" and self.group_assigned_menu:
            self.group_assigned_menu.dismiss()
        elif field == "Ticket Status" and self.ticket_status_menu:
            self.ticket_status_menu.dismiss()
        elif field in self.issue_type_menus:
            self.issue_type_menus[field].dismiss()
        elif field in self.issue_subtype_menus:
            self.issue_subtype_menus[field].dismiss()

    def get_task(self, instance):
        """Load task allocation data and show ticket owner selection popup"""
        try:
            # Connect to database and fetch data from Ticketing Task Allocation table
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            # Fetch all data from Ticketing Task Allocation table
            cursor.execute("SELECT * FROM [Ticketing Task Allocation]")
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            self.task_allocation_data = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[columns[i]] = value
                self.task_allocation_data.append(row_dict)

            conn.close()

            if not self.task_allocation_data:
                dialog = MDDialog(title="No Data", text="No tasks found in Ticketing Task Allocation table.")
                dialog.open()
                return

            # Get unique ticket owners
            ticket_owners = list(set([row.get('Ticket Owner', '') for row in self.task_allocation_data if row.get('Ticket Owner', '')]))
            ticket_owners.sort()

            if not ticket_owners:
                dialog = MDDialog(title="No Data", text="No Ticket Owners found in the data.")
                dialog.open()
                return

            # Show ticket owner selection popup
            self.show_ticket_owner_selection(ticket_owners)

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to load task data: {str(e)}")
            dialog.open()

    def show_ticket_owner_selection(self, ticket_owners):
        """Show modern popup with dropdown for ticket owner selection"""
        # Create modern content layout
        content_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(25),
            adaptive_height=True,
            size_hint_y=None,
            padding=dp(25)
        )
        content_layout.bind(minimum_height=content_layout.setter('height'))

        # Modern instruction label with icon
        instruction_card = MDCard(
            md_bg_color=[0.95, 0.97, 1, 1],
            elevation=4,
            radius=15,
            padding=dp(20),
            size_hint_y=None,
            height=dp(80)
        )
        instruction_label = MDLabel(
            text="🎯 Select a Ticket Owner to load tasks:",
            theme_text_color="Custom",
            text_color=[0.2, 0.3, 0.5, 1],
            font_style="H6",
            bold=True,
            halign="center"
        )
        instruction_card.add_widget(instruction_label)
        content_layout.add_widget(instruction_card)

        # Modern dropdown button
        self.ticket_owner_btn = MDRaisedButton(
            text="🔽 Select Ticket Owner",
            size_hint_y=None,
            height=dp(60),
            md_bg_color=[0.2, 0.6, 1, 1],
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=8,
            on_release=lambda x: self.open_ticket_owner_menu(ticket_owners)
        )
        content_layout.add_widget(self.ticket_owner_btn)

        # Create modern dialog
        self.ticket_owner_dialog = MDDialog(
            title="🚀 Get Task",
            type="custom",
            content_cls=content_layout,
            size_hint=(0.8, 0.6),
            buttons=[
                MDFlatButton(
                    text="Cancel",
                    theme_text_color="Custom",
                    text_color=[0.6, 0.6, 0.6, 1],
                    on_release=self.close_ticket_owner_dialog
                ),
                MDRaisedButton(
                    text="Load Tasks",
                    md_bg_color=[0.2, 0.8, 0.4, 1],
                    theme_text_color="Custom",
                    text_color=[1, 1, 1, 1],
                    elevation=6,
                    on_release=self.load_selected_tasks
                )
            ]
        )
        self.ticket_owner_dialog.open()

    def open_ticket_owner_menu(self, ticket_owners):
        """Open dropdown menu for ticket owner selection"""
        menu_items = []
        for owner in ticket_owners:
            menu_items.append({
                "text": owner,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=owner: self.select_ticket_owner(x)
            })

        self.ticket_owner_dropdown = MDDropdownMenu(
            caller=self.ticket_owner_btn,
            items=menu_items,
            width_mult=4
        )
        self.ticket_owner_dropdown.open()

    def select_ticket_owner(self, owner):
        """Handle ticket owner selection"""
        self.ticket_owner_btn.text = owner
        self.selected_ticket_owner = owner
        self.ticket_owner_dropdown.dismiss()

    def close_ticket_owner_dialog(self, instance):
        """Close ticket owner selection dialog"""
        self.ticket_owner_dialog.dismiss()

    def load_selected_tasks(self, instance):
        """Load tasks for selected ticket owner and populate form"""
        if not hasattr(self, 'selected_ticket_owner'):
            dialog = MDDialog(title="Error", text="Please select a Ticket Owner first.")
            dialog.open()
            return

        # Filter tasks for selected ticket owner
        owner_tasks = [task for task in self.task_allocation_data
                      if task.get('Ticket Owner', '') == self.selected_ticket_owner]

        if not owner_tasks:
            dialog = MDDialog(title="No Tasks", text=f"No tasks found for {self.selected_ticket_owner}.")
            dialog.open()
            return

        # Close the selection dialog
        self.ticket_owner_dialog.dismiss()

        # Populate form with first task and set up for cycling through tasks
        self.current_task_index = 0
        self.owner_tasks = owner_tasks
        self.populate_form_with_task(owner_tasks[0])

        # Update titles processed count based on unique Ticket IDs
        unique_ticket_ids = set([task.get('Ticket ID', '') for task in owner_tasks if task.get('Ticket ID', '')])
        self.titles_processed = len(unique_ticket_ids)
        self.titles_display.text = f"Titles Processed: {self.titles_processed}"

        dialog = MDDialog(title="Success", text=f"Loaded {len(owner_tasks)} tasks for {self.selected_ticket_owner}")
        dialog.open()

    def populate_form_with_task(self, task_data):
        """Populate form fields with task data"""
        try:
            # Map task data to form fields
            for field in fields:
                if field in task_data and task_data[field] is not None:
                    value = str(task_data[field])

                    if field in ["Offer Type", "Business Line", "Content Type", "Group Assigned",
                               "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2",
                               "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3"]:
                        # Handle dropdown fields
                        if value and value != "None":
                            self.entries[field].text = value
                        else:
                            # Reset to default text if no value
                            if field == "Offer Type":
                                self.entries[field].text = "Select Offer Type"
                            elif field == "Business Line":
                                self.entries[field].text = "Select Business Line"
                            elif field == "Content Type":
                                self.entries[field].text = "Select Content Type"
                            elif field == "Group Assigned":
                                self.entries[field].text = "Select Group Assigned"
                            elif field == "Ticket Status":
                                self.entries[field].text = "Select Ticket Status"
                            elif "Issue Type" in field:
                                self.entries[field].text = f"Select {field}"
                            elif "Issue Subtype" in field:
                                self.entries[field].text = f"Select {field}"
                    else:
                        # Handle text fields
                        if hasattr(self.entries[field], 'text'):
                            self.entries[field].text = value

            # Store current task data
            self.current_task_data = task_data

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to populate form: {str(e)}")
            dialog.open()



    def format_season_episode(self, instance):
        import re
        text = instance.text
        # Pattern to match S##-(episode_numbers) format
        formatted_text = re.sub(r'(S\d+)-(\([^)]+\))', r'\1-E\2', text)
        if formatted_text != text:
            instance.text = formatted_text

    def on_text_field_focus(self, instance, focus):
        """Handle text field focus to ensure cursor visibility"""
        if focus:
            # Ensure cursor is visible when text field gets focus
            Window.show_cursor = True
            try:
                Window.set_system_cursor('ibeam')  # Text cursor
            except:
                Window.set_system_cursor('arrow')  # Fallback to arrow
        else:
            # Reset to arrow cursor when losing focus
            try:
                Window.set_system_cursor('arrow')
            except:
                pass

    def on_season_episode_focus(self, instance, focus):
        # Call the general focus handler first
        self.on_text_field_focus(instance, focus)

        if not focus:  # When field loses focus
            self.format_season_episode(instance)

    def on_key_down(self, instance, keycode, text, modifiers):
        if keycode == 9:  # Tab key
            # Exclude all dropdown fields from tab navigation
            dropdown_fields = ["Offer Type", "Business Line", "Content Type", "Group Assigned",
                             "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2",
                             "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3"]
            field_names = [f for f in fields if f not in dropdown_fields]
            current_field = None
            for field, entry in self.entries.items():
                if entry == instance:
                    current_field = field
                    break
            if current_field and current_field in field_names:
                current_index = field_names.index(current_field)
                next_index = (current_index + 1) % len(field_names)
                next_field = self.entries[field_names[next_index]]
                next_field.focus = True
            return True
        return False

    def submit_data(self, data):
        user_id = getpass.getuser()
        today = datetime.date.today()
        week_num = today.isocalendar()[1]

        try:
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            insert_query = f"""
            INSERT INTO [Ticketing Process] (
                [Fresh/Backlog], [Ticket Owner], [Ticket ID], [Title Description], [Queue], [Created Date], 
                [Last Assigned], [Age], [DF Age], [Action Date],
                [Business Line], [Partner], [Market Place], [Identifiers(GTI/VCID/ASIN)], [Title], [Content Type], [Action Taken], [Group Assigned], [Single/Double], [Ticket Status], [Issue Type 1], [Issue Subtype 1], [Issue Type 2], [Issue Subtype 2], [Issue Type 3], [Issue Subtype 3], [Titles Received], [Titles Worked],
                [Comments], [Date], [Week], [User ID], [Start Time], [End Time], [Total Time],
                [Break Total Time]
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            self.end_time = datetime.datetime.now().time()

            # Calculate Total Time in minutes excluding break time
            if self.start_time:
                start_datetime = datetime.datetime.combine(today, self.start_time)
                end_datetime = datetime.datetime.combine(today, self.end_time)

                # Handle case where task spans midnight
                if end_datetime < start_datetime:
                    end_datetime = end_datetime + datetime.timedelta(days=1)

                gross_time = (end_datetime - start_datetime).total_seconds() / 60

                # Add any current break time if still in break mode
                if self.break_start_time:
                    current_break_time = datetime.datetime.now().time()
                    break_start_dt = datetime.datetime.combine(today, self.break_start_time)
                    current_break_dt = datetime.datetime.combine(today, current_break_time)

                    # Handle case where current break spans midnight
                    if current_break_dt < break_start_dt:
                        current_break_dt = current_break_dt + datetime.timedelta(days=1)

                    current_break_duration = (current_break_dt - break_start_dt).total_seconds() / 60
                    self.total_break_time += current_break_duration
                    # Reset break start time since we've accounted for it
                    self.break_start_time = None

                # Net time is gross time minus break time
                net_time = gross_time - self.total_break_time
                if net_time < 0:
                    net_time = 0  # Ensure we don't have negative time

                total_time = round(net_time, 2)
            else:
                # If no start time set, use current time as both start and end
                self.start_time = self.end_time
                total_time = 0.0

            # Create data list with proper field mapping and data type handling
            def get_field_value(field_name):
                if field_name == "Offer Type":
                    # Handle dropdown button text
                    offer_text = data.get(field_name, "")
                    if offer_text == "Select Offer Type":
                        return ""
                    return offer_text
                else:
                    return data.get(field_name, "")

            # Prepare data for insertion
            data_values = []
            for field in fields:
                value = get_field_value(field)
                # Ensure all values are strings and handle None values
                if value is None:
                    value = ""
                data_values.append(str(value))

            # Add empty values for Series GTI and Series Name since we removed task section
            data_values.extend(["", ""])  # Series GTI, Series Name

            # Add date/time info and break time
            final_data = []
            final_data.extend(data_values)
            final_data.extend([today, week_num, str(user_id), self.start_time, self.end_time, total_time])

            # Add only break total time to the final data list
            break_duration_value = getattr(self, 'break_duration_db', 0.0)
            formatted_break_duration = f"{break_duration_value:.2f}"  # Format as string with 2 decimal places

            # Only add Break Total Time to database
            final_data.append(formatted_break_duration)  # Break Total Time as string with 2 decimal places

            cursor.execute(insert_query, final_data)
            conn.commit()
            conn.close()

            # Update cumulative time and titles processed
            self.cumulative_time += total_time

            # Count titles processed based on unique Ticket IDs
            ticket_id = data.get("Ticket ID", "").strip()
            if ticket_id:
                # Initialize processed ticket IDs set if not exists
                if not hasattr(self, 'processed_ticket_ids'):
                    self.processed_ticket_ids = set()

                # Add current ticket ID to processed set
                self.processed_ticket_ids.add(ticket_id)
                self.titles_processed = len(self.processed_ticket_ids)
            else:
                # If no Ticket ID, increment by 1 (fallback behavior)
                self.titles_processed += 1

            # Update displays
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
            self.titles_display.text = f"Titles Processed: {self.titles_processed}"

            # Clear form after successful submission
            self.clear_form()

            # Reset timing for next task
            self.start_time = None
            self.end_time = None
            self.total_break_time = 0.0
            self.break_duration_db = 0.0

            dialog = MDDialog(title="Success", text="Data submitted successfully!")
            dialog.open()

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to submit data: {str(e)}")
            dialog.open()

    def preview_data(self, instance):
        data = {field: self.entries[field].text for field in fields}

        preview_text = "Data Preview:\n\n"
        for field, value in data.items():
            preview_text += f"{field}: {value}\n"

        preview_dialog = MDDialog(
            title="Data Preview",
            text=preview_text,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: preview_dialog.dismiss()),
                MDRaisedButton(text="Submit", on_release=lambda x: [preview_dialog.dismiss(), self.submit_data(data)])
            ]
        )
        preview_dialog.open()



    def add_new_form(self, instance):
        """Add a new additional form with modern styling"""
        self.form_counter += 1

        # Get the content layout (now directly under root)
        content = self.root.children[0]  # Get content layout directly

        # Create modern new form card
        new_form_card = MDCard(
            md_bg_color=[1, 1, 1, 1],  # Pure white
            elevation=12,
            radius=25,
            padding=dp(30),
            spacing=dp(20),
            adaptive_height=True
        )
        new_form_layout = MDBoxLayout(orientation="vertical", spacing=dp(20), adaptive_height=True)

        # Modern form title with styling
        title_card = MDCard(
            md_bg_color=[0.9, 0.95, 1, 1],  # Light blue
            elevation=4,
            radius=15,
            padding=dp(15),
            size_hint_y=None,
            height=dp(60)
        )
        form_title = MDLabel(
            text=f"📋 Additional Form {self.form_counter - 1}",
            theme_text_color="Custom",
            text_color=[0.2, 0.3, 0.5, 1],
            font_style="H5",
            bold=True,
            halign="center"
        )
        title_card.add_widget(form_title)
        new_form_layout.add_widget(title_card)

        # Create form entries dictionary for this form
        form_entries = {}

        # Add modern form fields
        for field in fields:
            # Modern field container for additional forms
            field_container = MDCard(
                md_bg_color=[0.98, 0.99, 1, 1],  # Very light blue
                elevation=2,
                radius=15,
                padding=dp(15),
                spacing=dp(10),
                adaptive_height=True,
                size_hint_y=None
            )

            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(15),
                                     adaptive_height=True, size_hint_y=None)

            # Modern label for additional forms
            label = MDLabel(
                text=field,
                size_hint_x=0.3,
                theme_text_color="Custom",
                text_color=[0.2, 0.3, 0.5, 1],
                font_style="Subtitle1",
                bold=True,
                halign="left"
            )
            field_layout.add_widget(label)

            if field in ["Offer Type", "Business Line", "Content Type", "Group Assigned", "Ticket Status",
                         "Issue Type 1", "Issue Subtype 1", "Issue Type 2", "Issue Subtype 2",
                         "Issue Type 3", "Issue Subtype 3"]:
                # Modern dropdown fields for additional forms
                # Create dropdown button with appropriate text
                if field == "Offer Type":
                    button_text = "Select Offer Type"
                elif field == "Business Line":
                    button_text = "Select Business Line"
                elif field == "Content Type":
                    button_text = "Select Content Type"
                elif field == "Group Assigned":
                    button_text = "Select Group Assigned"
                elif field == "Ticket Status":
                    button_text = "Select Ticket Status"
                elif "Issue Type" in field:
                    button_text = f"Select {field}"
                elif "Issue Subtype" in field:
                    button_text = f"Select {field}"
                else:
                    button_text = f"Select {field}"

                dropdown_field = MDRaisedButton(
                    text=button_text,
                    size_hint_x=0.7,
                    md_bg_color=[0.9, 0.95, 1, 1],  # Light blue
                    theme_text_color="Custom",
                    text_color=[0.2, 0.3, 0.5, 1],
                    elevation=4,
                    on_release=lambda x, fe=form_entries, f=field: self.open_dropdown_menu_for_form(x, fe, f)
                )
                form_entries[field] = dropdown_field
                field_layout.add_widget(dropdown_field)
            else:
                text_field = MDTextField(
                    text="",
                    multiline=True,
                    size_hint_x=0.7,
                    max_text_length=4000,
                    font_name='UnicodeFont',
                    mode="outlined",
                    line_color_normal=[0.7, 0.8, 0.9, 1],
                    line_color_focus=[0.2, 0.6, 1, 1],
                    text_color_normal=[0.2, 0.3, 0.5, 1],
                    text_color_focus=[0.1, 0.2, 0.4, 1],
                    fill_color_normal=[1, 1, 1, 1],
                    fill_color_focus=[0.98, 0.99, 1, 1]
                )
                text_field.height = dp(120)
                text_field.bind(text=self.on_text_change)
                text_field.bind(focus=self.on_text_field_focus)
                form_entries[field] = text_field
                field_layout.add_widget(text_field)

            field_layout.height = dp(120)
            field_container.add_widget(field_layout)
            new_form_layout.add_widget(field_container)

        # Modern Select button for this form
        select_btn_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(80),
            spacing=dp(10),
            padding=[dp(20), dp(10), dp(20), dp(10)]
        )
        select_btn_layout.add_widget(MDLabel(text="", size_hint_x=0.6))  # Spacer
        select_btn = MDRaisedButton(
            text="✅ Select This Form",
            size_hint_x=0.4,
            md_bg_color=[0.2, 0.6, 1, 1],  # Blue
            theme_text_color="Custom",
            text_color=[1, 1, 1, 1],
            elevation=8,
            on_release=lambda x, fe=form_entries: self.select_form(fe)
        )
        select_btn_layout.add_widget(select_btn)
        new_form_layout.add_widget(select_btn_layout)

        # Store form entries
        self.additional_forms.append(form_entries)

        new_form_card.add_widget(new_form_layout)

        # Insert new form at the end
        content.add_widget(new_form_card)

        # Modern success dialog
        dialog = MDDialog(
            title="✅ Success",
            text=f"Additional Form {self.form_counter - 1} has been added successfully!",
            buttons=[
                MDRaisedButton(
                    text="OK",
                    md_bg_color=[0.2, 0.8, 0.4, 1],
                    theme_text_color="Custom",
                    text_color=[1, 1, 1, 1],
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def open_offer_menu_for_form(self, instance, form_entries):
        """Open offer menu for specific form"""
        if not hasattr(self, 'selected_offers_forms'):
            self.selected_offers_forms = {}

        form_id = id(form_entries)
        if form_id not in self.selected_offers_forms:
            self.selected_offers_forms[form_id] = set()

        def toggle_form_offer_type(value):
            if value in self.selected_offers_forms[form_id]:
                self.selected_offers_forms[form_id].remove(value)
            else:
                self.selected_offers_forms[form_id].add(value)

            # Update button text
            if self.selected_offers_forms[form_id]:
                instance.text = ", ".join(sorted(self.selected_offers_forms[form_id]))
            else:
                instance.text = "Select Offer Type"

        menu_items = [
            {"text": "Prime", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("Prime")},
            {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("TVOD")},
            {"text": "Channels", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("Channels")},
            {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("FVOD")},
            {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("AVOD")},
            {"text": "POEST", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("POEST")},
            {"text": "SVOD", "viewclass": "OneLineListItem", "on_release": lambda: toggle_form_offer_type("SVOD")},
        ]

        menu = MDDropdownMenu(caller=instance, items=menu_items)
        menu.open()

    def open_dropdown_menu_for_form(self, instance, form_entries, field):
        """Open dropdown menu for additional form fields"""
        if field == "Offer Type":
            self.open_offer_menu_for_form(instance, form_entries)
        else:
            # Handle other dropdown fields for additional forms
            menu_items = []
            options = []

            if field == "Business Line":
                options = BUSINESS_LINE_OPTIONS
            elif field == "Content Type":
                options = CONTENT_TYPE_OPTIONS
            elif field == "Group Assigned":
                options = GROUP_ASSIGNED_OPTIONS
            elif field == "Ticket Status":
                options = TICKET_STATUS_OPTIONS
            elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
                options = ISSUE_TYPE_OPTIONS
            elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
                options = ISSUE_SUBTYPE_OPTIONS

            for option in options:
                menu_items.append({
                    "text": option,
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=option: self.set_form_dropdown_value(instance, x)
                })

            menu = MDDropdownMenu(caller=instance, items=menu_items)
            menu.open()

    def set_form_dropdown_value(self, button, value):
        """Set dropdown value for additional form fields"""
        button.text = value

    def select_form(self, form_entries):
        """Select which form is currently active"""
        self.active_form = form_entries
        dialog = MDDialog(title="Form Selected", text="This form is now active.")
        dialog.open()

    def remove_form(self, instance):
        """Remove the last additional form"""
        if self.additional_forms:
            # Remove from additional_forms list
            removed_form = self.additional_forms.pop()

            # Remove from UI
            scroll = self.root.children[0]  # Get scroll view
            content = scroll.children[0]    # Get content layout

            # Find and remove the last additional form card
            if len(content.children) > 1:  # Main form + at least one additional form
                content.remove_widget(content.children[0])  # Remove the last form
                self.form_counter -= 1

                dialog = MDDialog(title="Form Removed", text="Last additional form has been removed.")
                dialog.open()
            else:
                dialog = MDDialog(title="Info", text="No additional forms to remove.")
                dialog.open()
        else:
            dialog = MDDialog(title="Info", text="No additional forms to remove.")
            dialog.open()

    def set_break_mode(self, instance):
        """Set break mode - track break start time"""
        # Only set break start time if not already in break mode
        if not self.break_start_time:
            self.break_start_time = datetime.datetime.now().time()
            dialog = MDDialog(title="Break Mode", text="Break mode activated. Timer paused.")
            dialog.open()
        else:
            dialog = MDDialog(title="Info", text="Already in break mode.")
            dialog.open()

    def set_active_mode(self, instance):
        """Set active mode - calculate break duration and resume timer"""
        if self.break_start_time:
            # Calculate break duration
            break_end_time = datetime.datetime.now().time()
            today = datetime.date.today()

            break_start_dt = datetime.datetime.combine(today, self.break_start_time)
            break_end_dt = datetime.datetime.combine(today, break_end_time)

            # Handle case where break spans midnight
            if break_end_dt < break_start_dt:
                break_end_dt = break_end_dt + datetime.timedelta(days=1)

            break_duration = (break_end_dt - break_start_dt).total_seconds() / 60
            self.total_break_time += break_duration
            self.break_duration_db = self.total_break_time  # Store for database

            # Reset break start time
            self.break_start_time = None

            dialog = MDDialog(title="Active Mode", text=f"Active mode resumed. Break duration: {break_duration:.2f} minutes.")
            dialog.open()
        else:
            # Set start time if not already set
            if not self.start_time:
                self.start_time = datetime.datetime.now().time()
                dialog = MDDialog(title="Timer Started", text="Timer started for new task.")
                dialog.open()
            else:
                dialog = MDDialog(title="Info", text="Timer is already active.")
                dialog.open()

    def submit_task(self, instance):
        """Show preview popup before submitting task data"""
        # Collect all forms data
        all_forms_data = []

        # Main form data
        main_data = {field: self.entries[field].text if hasattr(self.entries[field], 'text') else self.entries[field].text for field in fields}
        all_forms_data.append(("Main Form", main_data))

        # Additional forms data
        for i, form_entries in enumerate(self.additional_forms):
            form_data = {}
            for field in fields:
                if field in form_entries:
                    if hasattr(form_entries[field], 'text'):
                        form_data[field] = form_entries[field].text
                    else:
                        form_data[field] = ""
                else:
                    form_data[field] = ""
            all_forms_data.append((f"Additional Form {i+1}", form_data))

        # Show preview dialog
        self.show_preview_dialog(all_forms_data)

    def show_preview_dialog(self, all_forms_data):
        """Show preview dialog with pagination"""
        self.current_preview_page = 0
        self.all_forms_preview_data = all_forms_data

        # Create dialog content
        self.preview_content = MDBoxLayout(orientation="vertical", spacing=dp(10),
                                         adaptive_height=True, size_hint_y=None)
        self.preview_content.bind(minimum_height=self.preview_content.setter('height'))

        # Create container for preview content (simplified without scroll)
        preview_container = MDBoxLayout(orientation="vertical", size_hint=(1, 0.8))
        preview_container.add_widget(self.preview_content)

        # Navigation buttons
        nav_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))

        self.prev_btn = MDFlatButton(text="Previous", on_release=self.prev_preview_page)
        self.next_btn = MDFlatButton(text="Next", on_release=self.next_preview_page)
        self.page_label = MDLabel(text="", size_hint_x=0.4, halign="center")

        nav_layout.add_widget(self.prev_btn)
        nav_layout.add_widget(self.page_label)
        nav_layout.add_widget(self.next_btn)

        # Main dialog layout
        dialog_content = MDBoxLayout(orientation="vertical", spacing=dp(10))
        dialog_content.add_widget(preview_container)
        dialog_content.add_widget(nav_layout)

        # Create dialog
        self.preview_dialog = MDDialog(
            title="Submit Preview",
            type="custom",
            content_cls=dialog_content,
            size_hint=(0.9, 0.9),
            buttons=[
                MDFlatButton(text="Cancel", on_release=self.close_preview_dialog),
                MDRaisedButton(text="Confirm Submit", on_release=self.confirm_submit)
            ]
        )

        # Load first page
        self.load_preview_page()
        self.preview_dialog.open()

    def load_preview_page(self):
        """Load current preview page"""
        self.preview_content.clear_widgets()

        if self.current_preview_page < len(self.all_forms_preview_data):
            form_name, form_data = self.all_forms_preview_data[self.current_preview_page]

            # Add form title
            title_label = MDLabel(text=form_name, font_style="H6", size_hint_y=None, height=dp(40), bold=True)
            self.preview_content.add_widget(title_label)

            # Add form fields
            for field, value in form_data.items():
                if value:  # Only show fields with values
                    field_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(30))
                    field_label = MDLabel(text=f"{field}:", size_hint_x=0.3, font_style="Subtitle2")
                    value_label = MDLabel(text=str(value), size_hint_x=0.7, font_style="Body2")
                    field_layout.add_widget(field_label)
                    field_layout.add_widget(value_label)
                    self.preview_content.add_widget(field_layout)

        # Update navigation
        self.update_preview_navigation()

    def update_preview_navigation(self):
        """Update preview navigation buttons and page label"""
        total_pages = len(self.all_forms_preview_data)
        current_page = self.current_preview_page + 1

        self.page_label.text = f"Page {current_page} of {total_pages}"
        self.prev_btn.disabled = self.current_preview_page == 0
        self.next_btn.disabled = self.current_preview_page >= total_pages - 1

    def prev_preview_page(self, instance):
        """Go to previous preview page"""
        if self.current_preview_page > 0:
            self.current_preview_page -= 1
            self.load_preview_page()

    def next_preview_page(self, instance):
        """Go to next preview page"""
        if self.current_preview_page < len(self.all_forms_preview_data) - 1:
            self.current_preview_page += 1
            self.load_preview_page()

    def close_preview_dialog(self, instance):
        """Close preview dialog"""
        self.preview_dialog.dismiss()

    def confirm_submit(self, instance):
        """Confirm and submit all forms data"""
        self.preview_dialog.dismiss()

        # Submit each form's data
        for form_name, form_data in self.all_forms_preview_data:
            self.submit_data(form_data)

        dialog = MDDialog(title="Success", text=f"Submitted {len(self.all_forms_preview_data)} forms successfully!")
        dialog.open()

    def clear_form(self):
        """Clear all form fields"""
        # Clear main form
        for field, entry in self.entries.items():
            if hasattr(entry, 'text'):
                if field == "Offer Type":
                    entry.text = "Select Offer Type"
                elif field == "Business Line":
                    entry.text = "Select Business Line"
                elif field == "Content Type":
                    entry.text = "Select Content Type"
                elif field == "Group Assigned":
                    entry.text = "Select Group Assigned"
                elif field == "Ticket Status":
                    entry.text = "Select Ticket Status"
                elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
                    entry.text = f"Select {field}"
                elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
                    entry.text = f"Select {field}"
                else:
                    entry.text = ""

        # Clear additional forms
        for form_entries in self.additional_forms:
            for field, entry in form_entries.items():
                if hasattr(entry, 'text'):
                    if field == "Offer Type":
                        entry.text = "Select Offer Type"
                    elif field == "Business Line":
                        entry.text = "Select Business Line"
                    elif field == "Content Type":
                        entry.text = "Select Content Type"
                    elif field == "Group Assigned":
                        entry.text = "Select Group Assigned"
                    elif field == "Ticket Status":
                        entry.text = "Select Ticket Status"
                    elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
                        entry.text = f"Select {field}"
                    elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
                        entry.text = f"Select {field}"
                    else:
                        entry.text = ""

        # Reset selections
        if hasattr(self, 'selected_offers'):
            self.selected_offers.clear()
        if hasattr(self, 'selected_offers_forms'):
            self.selected_offers_forms.clear()

        # Reset processed ticket IDs when clearing form
        self.processed_ticket_ids.clear()
        self.titles_processed = 0
        self.titles_display.text = "Titles Processed: 0"



    def auto_save_data(self):
        """Auto-save current form data"""
        try:
            # Collect form data
            form_data = {}
            for field, entry in self.entries.items():
                if hasattr(entry, 'text'):
                    form_data[field] = entry.text
                else:
                    form_data[field] = ""

            # Save state data
            state_data = {
                "form_data": form_data,
                "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                "cumulative_time": self.cumulative_time,
                "titles_processed": self.titles_processed,
                "processed_ticket_ids": list(self.processed_ticket_ids)
            }

            # Save to file
            with open(self.autosave_path, 'wb') as f:
                pickle.dump(state_data, f)

            print("Auto-saved form data")
        except Exception as e:
            print(f"Auto-save error: {e}")

    def try_load_autosave(self):
        """Try to load auto-saved data"""
        try:
            if os.path.exists(self.autosave_path):
                with open(self.autosave_path, 'rb') as f:
                    self._autosave_data = pickle.load(f)
                print("Auto-save data found")
            else:
                print("No auto-save data found")
        except Exception as e:
            print(f"Auto-save load error: {e}")

    def restore_autosaved_data(self):
        """Restore auto-saved data"""
        try:
            if hasattr(self, '_autosave_data'):
                state_data = self._autosave_data

                # Restore form data
                form_data = state_data.get("form_data", {})
                for field, value in form_data.items():
                    if field in self.entries and hasattr(self.entries[field], 'text'):
                        self.entries[field].text = value

                # Restore other state
                self.cumulative_time = state_data.get("cumulative_time", 0.0)
                self.titles_processed = state_data.get("titles_processed", 0)
                self.processed_ticket_ids = set(state_data.get("processed_ticket_ids", []))

                # Update displays
                self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
                self.titles_display.text = f"Titles Processed: {self.titles_processed}"

                # Restore start time
                start_time_str = state_data.get("start_time")
                if start_time_str:
                    self.start_time = datetime.datetime.strptime(start_time_str, "%H:%M:%S").time()

                print("Auto-saved data restored")
                return True
        except Exception as e:
            print(f"Restore error: {e}")
        return False

if __name__ == "__main__":
    TVSeriesForm().run()
